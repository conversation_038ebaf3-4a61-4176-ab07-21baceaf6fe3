import '../models/subject.dart';

class AcademicData {
  static List<AcademicYear> getAcademicYears() {
    return [
      // الفرقة الأولى
      AcademicYear(
        id: 'year1',
        name: 'First Year',
        arabicName: 'الفرقة الأولى',
        color: '#6366F1',
        gradientStart: '#6366F1',
        gradientEnd: '#8B5CF6',
        semesters: [
          Semester(
            id: 'year1_sem1',
            name: 'First Semester',
            arabicName: 'الترم الأول',
            subjects: [
              Subject(
                id: 'fiqh_issues',
                name: 'Contemporary Jurisprudential Issues',
                arabicName: 'قضايا فقهية معاصرة',
                credits: 2,
                pdfFiles: ['fiqh_issues.pdf'],
                description: 'دراسة القضايا الفقهية المعاصرة والمستجدات',
                lecturesCount: 2,
                examsCount: 1,
              ),
              Subject(
                id: 'legislation_history',
                name: 'History of Legislation',
                arabicName: 'تاريخ التشريع',
                credits: 2,
                pdfFiles: ['legislation_history.pdf'],
                description: 'تاريخ التشريع الإسلامي والقانوني',
                lecturesCount: 4,
                examsCount: 2,
              ),
              Subject(
                id: 'hadith_terminology',
                name: 'Hadith Terminology and Men',
                arabicName: 'مصطلح الحديث ورجاله',
                credits: 3,
                pdfFiles: ['hadith_terminology.pdf'],
                description: 'علم مصطلح الحديث ودراسة الرجال',
                lecturesCount: 3,
                examsCount: 1,
              ),
              Subject(
                id: 'criminology',
                name: 'Criminology and Punishment',
                arabicName: 'علم الإجرام والعقاب',
                credits: 2,
                pdfFiles: ['criminology.pdf'],
                description: 'دراسة علم الإجرام ونظريات العقاب',
                lecturesCount: 2,
                examsCount: 1,
              ),
              Subject(
                id: 'legal_history',
                name: 'History of Law',
                arabicName: 'تاريخ القانون',
                credits: 3,
                pdfFiles: ['legal_history.pdf'],
                description: 'تاريخ القانون وتطوره عبر العصور',
                lecturesCount: 3,
                examsCount: 1,
              ),
            ],
          ),
          Semester(
            id: 'year1_sem2',
            name: 'Second Semester',
            arabicName: 'الترم الثاني',
            subjects: [
              Subject(
                id: 'quran',
                name: 'Holy Quran',
                arabicName: 'القرآن الكريم',
                credits: 2,
                pdfFiles: ['quran.pdf'],
                description: 'دراسة القرآن الكريم وعلومه',
                lecturesCount: 2,
                examsCount: 1,
              ),
              Subject(
                id: 'international_law',
                name: 'Public International Law',
                arabicName: 'القانون الدولي العام',
                credits: 3,
                pdfFiles: ['international_law.pdf'],
                description: 'مبادئ القانون الدولي العام',
                lecturesCount: 4,
                examsCount: 2,
              ),
              Subject(
                id: 'usul_fiqh',
                name: 'Principles of Jurisprudence',
                arabicName: 'أصول الفقه (حنفي / غير حنفي)',
                credits: 4,
                pdfFiles: ['usul_fiqh.pdf'],
                description: 'أصول الفقه الإسلامي',
                lecturesCount: 4,
                examsCount: 2,
              ),
              Subject(
                id: 'constitutional_law',
                name: 'Constitutional Law',
                arabicName: 'القانون الدستوري',
                credits: 3,
                pdfFiles: ['constitutional_law.pdf'],
                description: 'مبادئ القانون الدستوري',
                lecturesCount: 3,
                examsCount: 1,
              ),
              Subject(
                id: 'administrative_law',
                name: 'Administrative Law',
                arabicName: 'القانون الإداري',
                credits: 3,
                pdfFiles: ['administrative_law.pdf'],
                description: 'أسس القانون الإداري',
                lecturesCount: 3,
                examsCount: 1,
              ),
            ],
          ),
        ],
      ),
      // الفرقة الثانية
      AcademicYear(
        id: 'year2',
        name: 'Second Year',
        arabicName: 'الفرقة الثانية',
        color: '#10B981',
        gradientStart: '#10B981',
        gradientEnd: '#059669',
        semesters: [
          Semester(
            id: 'year2_sem1',
            name: 'First Semester',
            arabicName: 'الترم الأول',
            subjects: [
              Subject(
                id: 'civil_law',
                name: 'Civil Law',
                arabicName: 'القانون المدني',
                credits: 4,
                pdfFiles: ['civil_law.pdf'],
                description: 'أحكام القانون المدني',
                lecturesCount: 4,
                examsCount: 2,
              ),
              Subject(
                id: 'criminal_law',
                name: 'Criminal Law',
                arabicName: 'القانون الجنائي',
                credits: 4,
                pdfFiles: ['criminal_law.pdf'],
                description: 'أحكام القانون الجنائي',
                lecturesCount: 4,
                examsCount: 2,
              ),
            ],
          ),
          Semester(
            id: 'year2_sem2',
            name: 'Second Semester',
            arabicName: 'الترم الثاني',
            subjects: [
              Subject(
                id: 'commercial_law',
                name: 'Commercial Law',
                arabicName: 'القانون التجاري',
                credits: 3,
                pdfFiles: ['commercial_law.pdf'],
                description: 'أحكام القانون التجاري',
                lecturesCount: 3,
                examsCount: 1,
              ),
            ],
          ),
        ],
      ),
    ];
  }
}
