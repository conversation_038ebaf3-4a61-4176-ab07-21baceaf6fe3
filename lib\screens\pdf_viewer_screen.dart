import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';
import '../models/subject.dart';
import '../theme/app_theme.dart';

class PDFViewerScreen extends StatefulWidget {
  final Subject subject;

  const PDFViewerScreen({
    super.key,
    required this.subject,
  });

  @override
  State<PDFViewerScreen> createState() => _PDFViewerScreenState();
}

class _PDFViewerScreenState extends State<PDFViewerScreen>
    with TickerProviderStateMixin {
  late PdfViewerController _pdfViewerController;
  late AnimationController _fabAnimationController;
  late Animation<double> _fabAnimation;
  
  bool _isLoading = true;
  bool _showControls = true;
  int _currentPage = 1;
  int _totalPages = 0;
  double _zoomLevel = 1.0;

  @override
  void initState() {
    super.initState();
    _pdfViewerController = PdfViewerController();
    
    _fabAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _fabAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fabAnimationController,
      curve: Curves.easeInOut,
    ));

    _fabAnimationController.forward();
  }

  @override
  void dispose() {
    _pdfViewerController.dispose();
    _fabAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: SafeArea(
        child: Column(
          children: [
            // الهيدر
            _buildHeader(),
            
            // عارض PDF
            Expanded(
              child: Stack(
                children: [
                  // PDF Viewer
                  Container(
                    margin: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: AppTheme.cardShadow,
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(16),
                      child: SfPdfViewer.asset(
                        'assets/pdfs/sample.pdf', // ملف تجريبي
                        controller: _pdfViewerController,
                        onDocumentLoaded: (PdfDocumentLoadedDetails details) {
                          setState(() {
                            _isLoading = false;
                            _totalPages = details.document.pages.count;
                          });
                        },
                        onPageChanged: (PdfPageChangedDetails details) {
                          setState(() {
                            _currentPage = details.newPageNumber;
                          });
                        },
                        onZoomLevelChanged: (PdfZoomDetails details) {
                          setState(() {
                            _zoomLevel = details.newZoomLevel;
                          });
                        },
                      ),
                    ),
                  ),
                  
                  // مؤشر التحميل
                  if (_isLoading)
                    Container(
                      margin: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: const Center(
                        child: CircularProgressIndicator(),
                      ),
                    ),
                  
                  // أدوات التحكم العائمة
                  if (_showControls && !_isLoading)
                    _buildFloatingControls(),
                ],
              ),
            ),
            
            // شريط المعلومات السفلي
            if (!_isLoading) _buildBottomInfo(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(Icons.arrow_back_ios),
            style: IconButton.styleFrom(
              backgroundColor: AppTheme.backgroundColor,
              padding: const EdgeInsets.all(8),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.subject.arabicName,
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'عارض PDF',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppTheme.textSecondaryColor,
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () {
              setState(() {
                _showControls = !_showControls;
              });
            },
            icon: Icon(_showControls ? Icons.visibility_off : Icons.visibility),
            style: IconButton.styleFrom(
              backgroundColor: AppTheme.backgroundColor,
              padding: const EdgeInsets.all(8),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFloatingControls() {
    return Positioned(
      top: 20,
      right: 20,
      child: FadeTransition(
        opacity: _fabAnimation,
        child: Column(
          children: [
            // زوم إن
            FloatingActionButton.small(
              onPressed: () {
                _pdfViewerController.zoomLevel = _zoomLevel * 1.25;
              },
              backgroundColor: Colors.white,
              foregroundColor: AppTheme.primaryColor,
              child: const Icon(Icons.zoom_in),
            ),
            const SizedBox(height: 8),
            
            // زوم أوت
            FloatingActionButton.small(
              onPressed: () {
                _pdfViewerController.zoomLevel = _zoomLevel * 0.8;
              },
              backgroundColor: Colors.white,
              foregroundColor: AppTheme.primaryColor,
              child: const Icon(Icons.zoom_out),
            ),
            const SizedBox(height: 8),
            
            // الصفحة السابقة
            FloatingActionButton.small(
              onPressed: _currentPage > 1 ? () {
                _pdfViewerController.previousPage();
              } : null,
              backgroundColor: Colors.white,
              foregroundColor: AppTheme.primaryColor,
              child: const Icon(Icons.keyboard_arrow_up),
            ),
            const SizedBox(height: 8),
            
            // الصفحة التالية
            FloatingActionButton.small(
              onPressed: _currentPage < _totalPages ? () {
                _pdfViewerController.nextPage();
              } : null,
              backgroundColor: Colors.white,
              foregroundColor: AppTheme.primaryColor,
              child: const Icon(Icons.keyboard_arrow_down),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomInfo() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            'الصفحة $_currentPage من $_totalPages',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
          Text(
            'التكبير: ${(_zoomLevel * 100).toInt()}%',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppTheme.textSecondaryColor,
            ),
          ),
        ],
      ),
    );
  }
}
